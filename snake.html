<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <style>
        body {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        canvas {
            border: 2px solid #333;
            margin-top: 20px;
        }
        .score {
            font-size: 24px;
            margin-top: 10px;
        }
        .controls {
            margin-top: 20px;
            font-size: 16px;
            color: #555;
        }
    </style>
</head>
<body>
    <h1>贪吃蛇游戏</h1>
    <div class="score">分数: <span id="score">0</span></div>
    <canvas id="gameCanvas" width="400" height="400"></canvas>
    <div class="controls">
        使用方向键控制蛇的移动
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const scoreElement = document.getElementById('score');
        
        const gridSize = 20;
        const tileCount = canvas.width / gridSize;
        
        let snake = [
            {x: 10, y: 10}
        ];
        let food = {
            x: Math.floor(Math.random() * tileCount),
            y: Math.floor(Math.random() * tileCount)
        };
        let velocityX = 0;
        let velocityY = 0;
        let score = 0;
        let gameSpeed = 150;
        let gameOver = false;
        let gameLoop;
        
        function drawGame() {
            // 清空画布
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制蛇
            ctx.fillStyle = 'green';
            snake.forEach(segment => {
                ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize-2, gridSize-2);
            });
            
            // 绘制食物
            ctx.fillStyle = 'red';
            ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize-2, gridSize-2);
            
            // 更新分数
            scoreElement.textContent = score;
            
            // 移动蛇
            const head = {x: snake[0].x + velocityX, y: snake[0].y + velocityY};
            
            // 检查碰撞
            if (
                head.x < 0 || head.x >= tileCount || 
                head.y < 0 || head.y >= tileCount ||
                snake.some(segment => segment.x === head.x && segment.y === head.y)
            ) {
                gameOver = true;
                clearInterval(gameLoop);
                alert('游戏结束! 你的分数: ' + score);
                return;
            }
            
            // 添加新头部
            snake.unshift(head);
            
            // 检查是否吃到食物
            if (head.x === food.x && head.y === food.y) {
                score += 10;
                food = {
                    x: Math.floor(Math.random() * tileCount),
                    y: Math.floor(Math.random() * tileCount)
                };
                
                // 加快游戏速度
                if (gameSpeed > 50) {
                    gameSpeed -= 5;
                    clearInterval(gameLoop);
                    gameLoop = setInterval(drawGame, gameSpeed);
                }
            } else {
                // 移除尾部
                snake.pop();
            }
        }
        
        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (gameOver) return;
            
            switch(e.key) {
                case 'ArrowUp':
                    if (velocityY !== 1) {
                        velocityX = 0;
                        velocityY = -1;
                    }
                    break;
                case 'ArrowDown':
                    if (velocityY !== -1) {
                        velocityX = 0;
                        velocityY = 1;
                    }
                    break;
                case 'ArrowLeft':
                    if (velocityX !== 1) {
                        velocityX = -1;
                        velocityY = 0;
                    }
                    break;
                case 'ArrowRight':
                    if (velocityX !== -1) {
                        velocityX = 1;
                        velocityY = 0;
                    }
                    break;
            }
        });
        
        // 开始游戏
        function startGame() {
            snake = [{x: 10, y: 10}];
            velocityX = 0;
            velocityY = 0;
            score = 0;
            gameSpeed = 150;
            gameOver = false;
            scoreElement.textContent = score;
            food = {
                x: Math.floor(Math.random() * tileCount),
                y: Math.floor(Math.random() * tileCount)
            };
            clearInterval(gameLoop);
            gameLoop = setInterval(drawGame, gameSpeed);
        }
        
        // 初始化游戏
        startGame();
        
        // 重新开始按钮
        const restartButton = document.createElement('button');
        restartButton.textContent = '重新开始';
        restartButton.style.marginTop = '10px';
        restartButton.addEventListener('click', startGame);
        document.body.appendChild(restartButton);
    </script>
</body>
</html>
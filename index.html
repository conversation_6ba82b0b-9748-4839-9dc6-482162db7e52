<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产运行业务流程第一阶段</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(90deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .workflow-container {
            padding: 30px;
            overflow-x: auto;
            min-height: 800px;
        }

        .workflow-stage {
            display: flex;
            flex-direction: column;
            margin-bottom: 40px;
            position: relative;
        }

        .stage-title {
            background: #2196F3;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .process-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .process-box {
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 500;
            text-align: center;
            min-width: 120px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
        }

        .process-box:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        /* 不同类型的流程框颜色 */
        .start-box { background: linear-gradient(135deg, #FF6B6B, #FF5252); }
        .process-box-blue { background: linear-gradient(135deg, #4FC3F7, #29B6F6); }
        .process-box-green { background: linear-gradient(135deg, #66BB6A, #4CAF50); }
        .process-box-orange { background: linear-gradient(135deg, #FFB74D, #FF9800); }
        .process-box-purple { background: linear-gradient(135deg, #BA68C8, #9C27B0); }
        .decision-box { 
            background: linear-gradient(135deg, #FFA726, #FF9800);
            border-radius: 50%;
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .arrow {
            font-size: 24px;
            color: #666;
            margin: 0 10px;
        }

        .vertical-arrow {
            text-align: center;
            font-size: 24px;
            color: #666;
            margin: 10px 0;
        }

        .sub-process {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #2196F3;
        }

        .sub-process-title {
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 15px;
        }

        .sub-process-items {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .sub-item {
            background: white;
            padding: 8px 15px;
            border-radius: 20px;
            border: 2px solid #e0e0e0;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sub-item:hover {
            border-color: #2196F3;
            background: #e3f2fd;
        }

        .flow-section {
            border: 2px dashed #ddd;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            background: #fafafa;
        }

        @media (max-width: 768px) {
            .process-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .arrow {
                transform: rotate(90deg);
            }
            
            .workflow-container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            生产运行业务流程第一阶段
        </div>
        
        <div class="workflow-container">
            <!-- 第一阶段：生产计划制定 -->
            <div class="workflow-stage">
                <div class="stage-title">生产计划制定阶段</div>
                
                <div class="process-row">
                    <div class="process-box start-box">生产计划启动</div>
                    <div class="arrow">→</div>
                    <div class="process-box process-box-blue">需求分析</div>
                    <div class="arrow">→</div>
                    <div class="process-box process-box-green">资源评估</div>
                    <div class="arrow">→</div>
                    <div class="decision-box">计划审核</div>
                </div>
                
                <div class="sub-process">
                    <div class="sub-process-title">需求分析详细步骤</div>
                    <div class="sub-process-items">
                        <div class="sub-item">市场需求调研</div>
                        <div class="sub-item">产能需求分析</div>
                        <div class="sub-item">技术要求评估</div>
                        <div class="sub-item">质量标准确定</div>
                    </div>
                </div>
            </div>

            <!-- 第二阶段：生产准备 -->
            <div class="workflow-stage">
                <div class="stage-title">生产准备阶段</div>
                
                <div class="flow-section">
                    <div class="process-row">
                        <div class="process-box process-box-orange">设备检查</div>
                        <div class="arrow">→</div>
                        <div class="process-box process-box-purple">人员配置</div>
                        <div class="arrow">→</div>
                        <div class="process-box process-box-blue">原料准备</div>
                    </div>
                    
                    <div class="vertical-arrow">↓</div>
                    
                    <div class="process-row">
                        <div class="process-box process-box-green">安全检查</div>
                        <div class="arrow">→</div>
                        <div class="decision-box">准备就绪确认</div>
                        <div class="arrow">→</div>
                        <div class="process-box start-box">生产启动</div>
                    </div>
                </div>
            </div>

            <!-- 第三阶段：生产执行 -->
            <div class="workflow-stage">
                <div class="stage-title">生产执行阶段</div>
                
                <div class="process-row">
                    <div class="process-box process-box-blue">生产作业</div>
                    <div class="arrow">→</div>
                    <div class="process-box process-box-green">质量监控</div>
                    <div class="arrow">→</div>
                    <div class="process-box process-box-orange">进度跟踪</div>
                    <div class="arrow">→</div>
                    <div class="process-box process-box-purple">异常处理</div>
                </div>
                
                <div class="sub-process">
                    <div class="sub-process-title">质量监控要点</div>
                    <div class="sub-process-items">
                        <div class="sub-item">实时数据监测</div>
                        <div class="sub-item">关键指标检查</div>
                        <div class="sub-item">质量标准对比</div>
                        <div class="sub-item">偏差分析处理</div>
                        <div class="sub-item">记录文档管理</div>
                    </div>
                </div>
            </div>

            <!-- 第四阶段：完成验收 -->
            <div class="workflow-stage">
                <div class="stage-title">完成验收阶段</div>
                
                <div class="flow-section">
                    <div class="process-row">
                        <div class="process-box process-box-green">产品检验</div>
                        <div class="arrow">→</div>
                        <div class="decision-box">质量验收</div>
                        <div class="arrow">→</div>
                        <div class="process-box process-box-blue">数据统计</div>
                    </div>
                    
                    <div class="vertical-arrow">↓</div>
                    
                    <div class="process-row">
                        <div class="process-box process-box-orange">报告生成</div>
                        <div class="arrow">→</div>
                        <div class="process-box process-box-purple">流程归档</div>
                        <div class="arrow">→</div>
                        <div class="process-box start-box">流程结束</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互功能
        document.querySelectorAll('.process-box, .decision-box').forEach(box => {
            box.addEventListener('click', function() {
                // 添加点击效果
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
                
                // 显示详细信息（可以根据需要扩展）
                console.log('点击了：', this.textContent);
            });
        });

        // 添加子项点击效果
        document.querySelectorAll('.sub-item').forEach(item => {
            item.addEventListener('click', function() {
                this.style.background = '#2196F3';
                this.style.color = 'white';
                setTimeout(() => {
                    this.style.background = 'white';
                    this.style.color = 'black';
                }, 1000);
            });
        });
    </script>
</body>
</html>
